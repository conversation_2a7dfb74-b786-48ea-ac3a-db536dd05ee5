"""
Use --settings=app.test_settings for running tests

example :
python manage.py test fields.tests.test_utils --settings=app.test_settings
"""

from django.test import TestCase, tag
import unittest
from fields.utils import is_event_within_work_shift, prepare_work_shifts_context
from fields.models import Field
from datetime import datetime, timezone as dt_timezone
from unittest.mock import Mock, patch
from decimal import Decimal


class BaseFieldUtilsTest(TestCase):
    """Base class for field utils tests with common setup"""
    def setUp(self):
        # Create a mock field with work shifts
        self.field = Mock()
        # Format the work shifts as expected by the is_event_within_work_shift function
        self.field.work_shifts = {
            "monday": ["08:00-16:00"],
            "tuesday": ["09:00-17:00"],
            "wednesday": [
                "08:00-12:00",
                "13:00-17:00"
            ]
        }


@tag('unit')
class FieldUtilsUnitTest(BaseFieldUtilsTest):
    """Unit tests for field utils focusing on individual function behavior"""

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_during_shift(self, mock_timezone):
        """Test that events during work shifts are correctly identified"""
        # Create a Monday at 10:00 AM
        monday_date = datetime(2024, 9, 30, 10, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '10:00'
        mock_datetime.strftime.return_value = 'monday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = monday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertTrue(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_outside_shift(self, mock_timezone):
        """Test that events outside work shifts are correctly identified"""
        # Create a Monday at 7:00 AM (before shift starts)
        monday_date = datetime(2024, 9, 30, 7, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '07:00'
        mock_datetime.strftime.return_value = 'monday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = monday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertFalse(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_multiple_shifts(self, mock_timezone):
        """Test that events during multiple work shifts are correctly identified"""
        # Create a Wednesday at 14:00 (during second shift)
        wednesday_date = datetime(2024, 10, 2, 14, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '14:00'
        mock_datetime.strftime.return_value = 'wednesday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = wednesday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertTrue(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_between_shifts(self, mock_timezone):
        """Test that events between multiple work shifts are correctly identified"""
        # Create a Wednesday at 12:30 (between shifts)
        wednesday_date = datetime(2024, 10, 2, 12, 30, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '12:30'
        mock_datetime.strftime.return_value = 'wednesday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = wednesday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertFalse(result)

    def test_prepare_work_shifts_context(self):
        """Test that work shifts context is correctly prepared"""
        # Create a mock field with work shifts in the expected format
        mock_field = Mock()
        mock_field.work_shifts = {
            "monday": ["08:00-16:00"],
            "tuesday": ["09:00-17:00"],
            "wednesday": [
                "08:00-12:00",
                "13:00-17:00"
            ]
        }

        context = prepare_work_shifts_context(mock_field)

        # Check that the context is a list
        self.assertIsInstance(context, list)

        # Create a dictionary to group shifts by day for easier testing
        shifts_by_day = {}
        for shift in context:
            day = shift['day']
            shifts_by_day.setdefault(day, []).append(shift)

        # Check that all days are in the context
        self.assertIn('monday', shifts_by_day)
        self.assertIn('tuesday', shifts_by_day)
        self.assertIn('wednesday', shifts_by_day)

        # Check that the shifts are correctly formatted
        self.assertEqual(shifts_by_day['monday'][0]['start_time'], '08:00')
        self.assertEqual(shifts_by_day['monday'][0]['end_time'], '16:00')

        # Check that multiple shifts are handled correctly
        self.assertEqual(len(shifts_by_day['wednesday']), 2)

        # Sort the shifts by start_time to ensure consistent order
        wednesday_shifts = sorted(shifts_by_day['wednesday'], key=lambda x: x['start_time'])
        self.assertEqual(wednesday_shifts[0]['start_time'], '08:00')
        self.assertEqual(wednesday_shifts[1]['start_time'], '13:00')

    def test_is_event_within_work_shift_exception_handling(self):
        """Test is_event_within_work_shift error handling with invalid timestamp"""
        # Test with an invalid timestamp format
        result = is_event_within_work_shift(self.field, "invalid-timestamp")

        # Function should return False when an exception occurs
        self.assertFalse(result)

    def test_is_event_within_work_shift_invalid_shift_format(self):
        """Test is_event_within_work_shift with invalid work shift format"""
        # Create a mock field with invalid work shift format (start > end)
        mock_field = Mock()
        mock_field.work_shifts = {
            "monday": ["16:00-08:00"]  # Invalid: start > end
        }

        # Create a Monday at 10:00 AM
        monday_date = datetime(2024, 9, 30, 10, 0, 0, tzinfo=dt_timezone.utc)
        event_timestamp = monday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(mock_field, event_timestamp)

        # Function should return False for invalid shift format
        self.assertFalse(result)

    def test_prepare_work_shifts_context_empty_shifts(self):
        """Test prepare_work_shifts_context with empty work shifts"""
        # Create a mock field with empty work shifts
        mock_field = Mock()
        mock_field.work_shifts = {}  # Empty work shifts

        # Test the function
        context = prepare_work_shifts_context(mock_field)

        # Verify the result is an empty list
        self.assertEqual(context, [])

    def test_sort_work_shifts_function(self):
        """Test the sort_work_shifts utility function"""
        from fields.utils import sort_work_shifts

        # Create unsorted work shifts
        unsorted_shifts = [
            {"day": "friday", "start_time": "08:00", "end_time": "16:00"},
            {"day": "monday", "start_time": "09:00", "end_time": "17:00"},
            {"day": "wednesday", "start_time": "07:00", "end_time": "15:00"},
            {"day": "monday", "start_time": "08:00", "end_time": "16:00"},
        ]

        # Sort the shifts
        sorted_shifts = sort_work_shifts(unsorted_shifts)

        # Verify sorting order
        self.assertEqual(sorted_shifts[0]["day"], "monday")
        self.assertEqual(sorted_shifts[0]["start_time"], "08:00")
        self.assertEqual(sorted_shifts[1]["day"], "monday")
        self.assertEqual(sorted_shifts[1]["start_time"], "09:00")
        self.assertEqual(sorted_shifts[2]["day"], "wednesday")
        self.assertEqual(sorted_shifts[3]["day"], "friday")

    def test_get_work_shifts_from_form_function(self):
        """Test the get_work_shifts_from_form utility function"""
        from fields.utils import get_work_shifts_from_form
        from django.http import QueryDict

        # Create a mock request with POST data
        mock_request = Mock()
        post_data = QueryDict(mutable=True)
        post_data.setlist('day', ['monday', 'tuesday'])
        post_data.setlist('start_time', ['08:00', '09:00'])
        post_data.setlist('end_time', ['16:00', '17:00'])
        mock_request.POST = post_data

        # Test the function
        work_shifts = get_work_shifts_from_form(mock_request)

        # Verify the result
        expected_shifts = {
            'monday': ['08:00-16:00'],
            'tuesday': ['09:00-17:00']
        }
        self.assertEqual(work_shifts, expected_shifts)


@tag('integration')
class FieldUtilsIntegrationTest(BaseFieldUtilsTest):
    """Integration tests for field utils with external dependencies"""

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_with_mocked_timezone(self, mock_timezone):
        """Test is_event_within_work_shift with a mocked timezone function"""
        # Create a mock datetime object for Monday at 10:00 AM
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '10:00'
        mock_datetime.strftime.return_value = 'monday'

        # Configure the mock timezone to return our mock datetime
        mock_timezone.localtime.return_value = mock_datetime

        # Create a test timestamp
        event_timestamp = '2024-09-30T10:00:00+00:00'

        # Test the function
        result = is_event_within_work_shift(self.field, event_timestamp)

        # Verify the result and that the mocks were called correctly
        self.assertTrue(result)
        mock_timezone.localtime.assert_called_once()
        mock_datetime.time.assert_called_once()
        mock_datetime.strftime.assert_called_once_with("%A")

    def test_is_event_within_work_shift_with_mocked_field(self):
        """Test is_event_within_work_shift with a mocked Field object"""
        # Create a mock Field object
        mock_field = Mock()
        mock_field.work_shifts = {
            'monday': ['09:00-17:00'],
            'tuesday': ['08:00-16:00']
        }

        # Create a Monday at 10:00 AM
        monday_date = datetime(2024, 9, 30, 10, 0, 0, tzinfo=dt_timezone.utc)
        event_timestamp = monday_date.isoformat()

        # Test the function
        result = is_event_within_work_shift(mock_field, event_timestamp)

        # Verify the result
        self.assertTrue(result)

    def test_prepare_work_shifts_context_with_mocked_field(self):
        """Test prepare_work_shifts_context with a mocked Field object"""
        # Create a mock Field object
        mock_field = Mock()
        mock_field.work_shifts = {
            'monday': ['08:00-16:00'],
            'tuesday': ['09:00-17:00']
        }

        # Test the function
        context = prepare_work_shifts_context(mock_field)

        # Verify the result
        self.assertEqual(len(context), 2)  # Two days

        # Create a dictionary to group shifts by day for easier testing
        shifts_by_day = {}
        for shift in context:
            day = shift['day']
            shifts_by_day.setdefault(day, []).append(shift)

        # Check that all days are in the context
        self.assertIn('monday', shifts_by_day)
        self.assertIn('tuesday', shifts_by_day)

        # Check that the shifts are correctly formatted
        self.assertEqual(shifts_by_day['monday'][0]['start_time'], '08:00')
        self.assertEqual(shifts_by_day['monday'][0]['end_time'], '16:00')
        self.assertEqual(shifts_by_day['tuesday'][0]['start_time'], '09:00')
        self.assertEqual(shifts_by_day['tuesday'][0]['end_time'], '17:00')


@tag('e2e')
class FieldUtilsE2ETest(unittest.TestCase):
    """End-to-end tests for field utils using realistic Field objects

    This test class uses realistic Field objects that are created as instances of the Field class
    rather than using Mock objects. This allows us to test complete workflows with objects that
    behave like real database objects without requiring an actual database connection.
    """

    def setUp(self):
        """Set up test data with realistic Field objects"""
        # Create a Field object with work shifts
        self.field = Field(
            name="Real Test Field",
            cord=[{"lat": 23.5674, "lng": 58.3248}],
            colr="#FF5733",
            covr=Decimal("1.234"),
            loca="Test Location",
            work_shifts={
                "monday": ["08:00-16:00"],
                "tuesday": ["09:00-17:00"],
                "wednesday": [
                    "08:00-12:00",
                    "13:00-17:00"
                ]
            }
        )
        # Set an ID to simulate a saved object
        self.field.id = 1

        # Create another field with different work shifts for testing
        self.field2 = Field(
            name="Second Real Test Field",
            cord=[{"lat": 24.5674, "lng": 59.3248}],
            colr="#33FF57",
            covr=Decimal("2.345"),
            loca="Another Location",
            work_shifts={
                "monday": ["09:00-17:00"],
                "friday": ["08:00-14:00"]
            }
        )
        # Set an ID to simulate a saved object
        self.field2.id = 2

        # Create a field with empty work shifts
        self.empty_field = Field(
            name="Empty Work Shifts Field",
            cord=[{"lat": 25.5674, "lng": 60.3248}],
            colr="#5733FF",
            covr=Decimal("3.456"),
            loca="Empty Location",
            work_shifts={}
        )
        # Set an ID to simulate a saved object
        self.empty_field.id = 3

        # Create a field with invalid work shifts format
        self.invalid_field = Field(
            name="Invalid Work Shifts Field",
            cord=[{"lat": 26.5674, "lng": 61.3248}],
            colr="#57FF33",
            covr=Decimal("4.567"),
            loca="Invalid Location",
            work_shifts={
                "monday": ["16:00-08:00"]  # Invalid: start > end
            }
        )
        # Set an ID to simulate a saved object
        self.invalid_field.id = 4

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_during_shift(self, mock_timezone):
        """Test is_event_within_work_shift with a real Field object during work shift"""
        # Create a Monday at 10:00 AM
        monday_date = datetime(2024, 9, 30, 10, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '10:00'
        mock_datetime.strftime.return_value = 'monday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = monday_date.isoformat()

        # Test the function with a real Field object
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertTrue(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_outside_shift(self, mock_timezone):
        """Test is_event_within_work_shift with a real Field object outside work shift"""
        # Create a Monday at 7:00 AM (before shift starts)
        monday_date = datetime(2024, 9, 30, 7, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '07:00'
        mock_datetime.strftime.return_value = 'monday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = monday_date.isoformat()

        # Test the function with a real Field object
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertFalse(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_multiple_shifts(self, mock_timezone):
        """Test is_event_within_work_shift with a real Field object with multiple shifts"""
        # Create a Wednesday at 14:00 (during second shift)
        wednesday_date = datetime(2024, 10, 2, 14, 0, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '14:00'
        mock_datetime.strftime.return_value = 'wednesday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = wednesday_date.isoformat()

        # Test the function with a real Field object
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertTrue(result)

    @patch('fields.utils.timezone')
    def test_is_event_within_work_shift_between_shifts(self, mock_timezone):
        """Test is_event_within_work_shift with a real Field object between shifts"""
        # Create a Wednesday at 12:30 (between shifts)
        wednesday_date = datetime(2024, 10, 2, 12, 30, 0, tzinfo=dt_timezone.utc)

        # Mock the timezone.localtime function
        mock_datetime = Mock()
        mock_datetime.time.return_value.strftime.return_value = '12:30'
        mock_datetime.strftime.return_value = 'wednesday'
        mock_timezone.localtime.return_value = mock_datetime

        # Convert to ISO format for the function
        event_timestamp = wednesday_date.isoformat()

        # Test the function with a real Field object
        result = is_event_within_work_shift(self.field, event_timestamp)
        self.assertFalse(result)

    def test_prepare_work_shifts_context(self):
        """Test prepare_work_shifts_context with a real Field object"""
        # Test the function with a real Field object
        context = prepare_work_shifts_context(self.field)

        # Check that the context is a list
        self.assertIsInstance(context, list)

        # Create a dictionary to group shifts by day for easier testing
        shifts_by_day = {}
        for shift in context:
            day = shift['day']
            shifts_by_day.setdefault(day, []).append(shift)

        # Check that all days are in the context
        self.assertIn('monday', shifts_by_day)
        self.assertIn('tuesday', shifts_by_day)
        self.assertIn('wednesday', shifts_by_day)

        # Check that the shifts are correctly formatted
        self.assertEqual(shifts_by_day['monday'][0]['start_time'], '08:00')
        self.assertEqual(shifts_by_day['monday'][0]['end_time'], '16:00')

        # Check that multiple shifts are handled correctly
        self.assertEqual(len(shifts_by_day['wednesday']), 2)

        # Sort the shifts by start_time to ensure consistent order
        wednesday_shifts = sorted(shifts_by_day['wednesday'], key=lambda x: x['start_time'])
        self.assertEqual(wednesday_shifts[0]['start_time'], '08:00')
        self.assertEqual(wednesday_shifts[1]['start_time'], '13:00')

    def test_prepare_work_shifts_context_empty_shifts(self):
        """Test prepare_work_shifts_context with a real Field object with empty work shifts"""
        # Test the function with a real Field object with empty work shifts
        context = prepare_work_shifts_context(self.empty_field)

        # Verify the result is an empty list
        self.assertEqual(context, [])

    def test_is_event_within_work_shift_invalid_shift_format(self):
        """Test is_event_within_work_shift with a real Field object with invalid work shift format"""
        # Create a Monday at 10:00 AM
        monday_date = datetime(2024, 9, 30, 10, 0, 0, tzinfo=dt_timezone.utc)
        event_timestamp = monday_date.isoformat()

        # Test the function with a real Field object with invalid work shift format
        result = is_event_within_work_shift(self.invalid_field, event_timestamp)

        # Function should return False for invalid shift format
        self.assertFalse(result)

    def test_is_event_within_work_shift_exception_handling(self):
        """Test is_event_within_work_shift error handling with invalid timestamp using a real Field object"""
        # Test with an invalid timestamp format
        result = is_event_within_work_shift(self.field, "invalid-timestamp")

        # Function should return False when an exception occurs
        self.assertFalse(result)

    def test_different_fields_have_different_work_shifts(self):
        """Test that different Field objects have different work shifts"""
        # Verify that the two fields have different work shifts
        self.assertNotEqual(self.field.work_shifts, self.field2.work_shifts)

        # Since we're not using a database, we can't test retrieving from the database
        # Instead, we'll verify the work_shifts attribute directly
        self.assertEqual(self.field.work_shifts, {
            "monday": ["08:00-16:00"],
            "tuesday": ["09:00-17:00"],
            "wednesday": [
                "08:00-12:00",
                "13:00-17:00"
            ]
        })
        self.assertEqual(self.field2.work_shifts, {
            "monday": ["09:00-17:00"],
            "friday": ["08:00-14:00"]
        })