import json
from django.test import TestCase, tag
from fields.models import Field
from decimal import Decimal
from unittest.mock import patch, Mock


class BaseFieldModelTest(TestCase):
    """Base class for field model tests with common setup"""
    def setUp(self):
        self.field_data = {
            "name": "Test Field",
            "cord": [{"lat": 23.5674, "lng": 58.3248}],
            "colr": "#FF5733",
            "covr": Decimal("1.234"),
            "loca": "Test Location",
            "work_shifts": {
                "monday": [{"start": "08:00", "end": "16:00"}]
            }
        }
        self.field = Field.objects.create(**self.field_data)


@tag('unmock')
class FieldModelUnmockTest(BaseFieldModelTest):
    """Tests for field model without using mocks"""

    def test_field_creation(self):
        """Test that a field can be created with the expected attributes"""
        self.assertEqual(self.field.name, "Test Field")
        self.assertEqual(self.field.cord, [{"lat": 23.5674, "lng": 58.3248}])
        self.assertEqual(self.field.colr, "#FF5733")
        self.assertEqual(self.field.covr, Decimal("1.234"))
        self.assertEqual(self.field.loca, "Test Location")
        self.assertEqual(self.field.work_shifts, {"monday": [{"start": "08:00", "end": "16:00"}]})

    def test_to_dict(self):
        """Test the to_dict method returns the expected dictionary"""
        field_dict = self.field.to_dict()
        self.assertEqual(field_dict["id"], self.field.id)
        self.assertEqual(field_dict["name"], "Test Field")
        self.assertEqual(field_dict["cord"], [{"lat": 23.5674, "lng": 58.3248}])
        self.assertEqual(field_dict["colr"], "#FF5733")
        self.assertEqual(field_dict["covr"], float(Decimal("1.234")))
        self.assertEqual(field_dict["loca"], "Test Location")
        self.assertEqual(field_dict["work_shifts"], {"monday": [{"start": "08:00", "end": "16:00"}]})

    def test_to_json(self):
        """Test the to_json method returns a valid JSON string"""
        json_str = self.field.to_json()
        data = json.loads(json_str)
        self.assertEqual(data["name"], "Test Field")
        self.assertEqual(data["covr"], float(Decimal("1.234")))

    def test_from_json(self):
        """Test the from_json static method creates a Field object from JSON"""
        json_data = json.dumps({
            "id": 1,
            "name": "JSON Field",
            "cord": [{"lat": 23.1, "lng": 58.2}],
            "colr": "#AABBCC",
            "covr": 2.345,
            "loca": "JSON Location",
            "work_shifts": {"tuesday": [{"start": "09:00", "end": "17:00"}]}
        })

        field = Field.from_json(json_data)
        self.assertEqual(field.name, "JSON Field")
        self.assertEqual(field.colr, "#AABBCC")
        self.assertEqual(field.covr, 2.345)

    def test_str_representation(self):
        """Test the string representation of a Field object"""
        self.assertEqual(str(self.field), "Test Field")


@tag('mock')
class FieldModelMockTest(BaseFieldModelTest):
    """Tests for field model using mocks"""

    @patch('fields.models.async_to_sync')
    @patch('fields.models.get_channel_layer')
    def test_post_save_signal(self, mock_get_channel_layer, mock_async_to_sync):
        """Test that the post_save signal sends an update to the channel layer"""
        # Setup mocks
        mock_async_to_sync.return_value = lambda *_: None  # Simplified lambda that ignores all arguments

        # Save the field to trigger the signal
        self.field.name = "Updated Field"
        self.field.save()

        # Check that the channel layer was called correctly
        mock_get_channel_layer.assert_called_once()
        mock_async_to_sync.assert_called_once()

    def test_to_dict_with_mocked_field(self):
        """Test to_dict method with a mocked Field object"""
        # Create a mock Field object
        mock_field = Mock()
        mock_field.id = 999
        mock_field.name = "Mocked Field"
        mock_field.cord = [{"lat": 10.0, "lng": 20.0}]
        mock_field.colr = "#000000"
        mock_field.covr = 5.678
        mock_field.loca = "Mocked Location"
        mock_field.work_shifts = {"friday": [{"start": "10:00", "end": "18:00"}]}

        # Add the to_dict method to the mock
        mock_field.to_dict = Field.to_dict.__get__(mock_field, Field)

        # Test the method
        field_dict = mock_field.to_dict()
        self.assertEqual(field_dict["id"], 999)
        self.assertEqual(field_dict["name"], "Mocked Field")
        self.assertEqual(field_dict["colr"], "#000000")
        self.assertEqual(field_dict["covr"], 5.678)

    def test_to_json_with_mocked_field(self):
        """Test to_json method with a mocked Field object"""
        # Create a mock Field object
        mock_field = Mock()
        mock_field.id = 888
        mock_field.name = "JSON Mocked Field"
        mock_field.cord = [{"lat": 30.0, "lng": 40.0}]
        mock_field.colr = "#FFFFFF"
        mock_field.covr = 9.876
        mock_field.loca = "JSON Mocked Location"
        mock_field.work_shifts = {"saturday": [{"start": "09:00", "end": "17:00"}]}

        # Add the to_dict and to_json methods to the mock
        mock_field.to_dict = lambda: {
            "id": mock_field.id,
            "name": mock_field.name,
            "cord": mock_field.cord,
            "colr": mock_field.colr,
            "covr": mock_field.covr,
            "loca": mock_field.loca,
            "work_shifts": mock_field.work_shifts
        }
        mock_field.to_json = Field.to_json.__get__(mock_field, Field)

        # Test the method
        json_str = mock_field.to_json()
        data = json.loads(json_str)
        self.assertEqual(data["id"], 888)
        self.assertEqual(data["name"], "JSON Mocked Field")
        self.assertEqual(data["colr"], "#FFFFFF")