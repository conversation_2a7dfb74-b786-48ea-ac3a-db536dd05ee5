"""
Use --settings=app.test_settings for running tests

example :
python manage.py test fields.tests.test_models --settings=app.test_settings
"""

import json
from django.test import TestCase, tag
from fields.models import Field
from decimal import Decimal
from unittest.mock import patch


class BaseFieldModelTest(TestCase):
    """Base class for field model tests with common setup"""
    def setUp(self):
        self.field_data = {
            "name": "Test Field",
            "cord": [{"lat": 23.5674, "lng": 58.3248}],
            "colr": "#FF5733",
            "covr": Decimal("1.234"),
            "loca": "Test Location",
            "work_shifts": {
                "monday": [{"start": "08:00", "end": "16:00"}]
            }
        }
        self.field = Field.objects.create(**self.field_data)


@tag('unit')
class FieldModelUnitTest(BaseFieldModelTest):
    """Unit tests for field model methods and properties"""

    def test_field_creation(self):
        """Test that a field can be created with the expected attributes"""
        self.assertEqual(self.field.name, "Test Field")
        self.assertEqual(self.field.cord, [{"lat": 23.5674, "lng": 58.3248}])
        self.assertEqual(self.field.colr, "#FF5733")
        self.assertEqual(self.field.covr, Decimal("1.234"))
        self.assertEqual(self.field.loca, "Test Location")
        self.assertEqual(self.field.work_shifts, {"monday": [{"start": "08:00", "end": "16:00"}]})

    def test_to_dict(self):
        """Test the to_dict method returns the expected dictionary"""
        field_dict = self.field.to_dict()
        self.assertEqual(field_dict["id"], self.field.id)
        self.assertEqual(field_dict["name"], "Test Field")
        self.assertEqual(field_dict["cord"], [{"lat": 23.5674, "lng": 58.3248}])
        self.assertEqual(field_dict["colr"], "#FF5733")
        self.assertEqual(field_dict["covr"], float(Decimal("1.234")))
        self.assertEqual(field_dict["loca"], "Test Location")
        self.assertEqual(field_dict["work_shifts"], {"monday": [{"start": "08:00", "end": "16:00"}]})

    def test_to_json(self):
        """Test the to_json method returns a valid JSON string"""
        json_str = self.field.to_json()
        data = json.loads(json_str)
        self.assertEqual(data["name"], "Test Field")
        self.assertEqual(data["covr"], float(Decimal("1.234")))

    def test_from_json(self):
        """Test the from_json static method creates a Field object from JSON"""
        json_data = json.dumps({
            "id": 1,
            "name": "JSON Field",
            "cord": [{"lat": 23.1, "lng": 58.2}],
            "colr": "#AABBCC",
            "covr": 2.345,
            "loca": "JSON Location",
            "work_shifts": {"tuesday": [{"start": "09:00", "end": "17:00"}]}
        })

        field = Field.from_json(json_data)
        self.assertEqual(field.name, "JSON Field")
        self.assertEqual(field.colr, "#AABBCC")
        self.assertEqual(field.covr, 2.345)

    def test_str_representation(self):
        """Test the string representation of a Field object"""
        self.assertEqual(str(self.field), "Test Field")

    def test_field_validation_edge_cases(self):
        """Test field creation with edge case values"""
        # Test with empty coordinates
        field_with_empty_coords = Field.objects.create(
            name="Empty Coords Field",
            cord=[],
            colr="#000000",
            covr=Decimal("0.000"),
            loca="",
            work_shifts={}
        )
        self.assertEqual(field_with_empty_coords.cord, [])
        self.assertEqual(field_with_empty_coords.covr, Decimal("0.000"))

    def test_to_dict_with_none_values(self):
        """Test to_dict method handles None values gracefully"""
        field = Field(
            name="Test Field",
            cord=None,
            colr="#FF5733",
            covr=None,
            loca=None,
            work_shifts=None
        )
        field.id = 999  # Set ID manually since we're not saving
        field_dict = field.to_dict()
        self.assertEqual(field_dict["name"], "Test Field")
        self.assertIsNone(field_dict["cord"])
        self.assertIsNone(field_dict["loca"])


@tag('integration')
class FieldModelIntegrationTest(BaseFieldModelTest):
    """Integration tests for field model with external dependencies"""

    @patch('fields.models.async_to_sync')
    @patch('fields.models.get_channel_layer')
    def test_post_save_signal_integration(self, mock_get_channel_layer, mock_async_to_sync):
        """Test that the post_save signal integrates correctly with channel layer"""
        # Setup mocks
        mock_async_to_sync.return_value = lambda *_: None  # Simplified lambda that ignores all arguments

        # Save the field to trigger the signal
        self.field.name = "Updated Field"
        self.field.save()

        # Check that the channel layer was called correctly
        mock_get_channel_layer.assert_called_once()
        mock_async_to_sync.assert_called_once()

    def test_field_serialization_roundtrip(self):
        """Test complete serialization and deserialization workflow"""
        # Serialize field to JSON
        json_str = self.field.to_json()

        # Deserialize back to Field object
        deserialized_field = Field.from_json(json_str)

        # Verify all attributes match
        self.assertEqual(deserialized_field.name, self.field.name)
        self.assertEqual(deserialized_field.cord, self.field.cord)
        self.assertEqual(deserialized_field.colr, self.field.colr)
        self.assertEqual(deserialized_field.covr, self.field.covr)
        self.assertEqual(deserialized_field.loca, self.field.loca)
        self.assertEqual(deserialized_field.work_shifts, self.field.work_shifts)

    def test_field_update_workflow(self):
        """Test complete field update workflow"""
        original_name = self.field.name

        # Update field
        self.field.name = "Updated Field Name"
        self.field.colr = "#AABBCC"
        self.field.save()

        # Refresh from database
        self.field.refresh_from_db()

        # Verify changes persisted
        self.assertEqual(self.field.name, "Updated Field Name")
        self.assertEqual(self.field.colr, "#AABBCC")
        self.assertNotEqual(self.field.name, original_name)

    def test_multiple_fields_creation(self):
        """Test creating multiple fields and their interactions"""
        # Create additional fields
        Field.objects.create(
            name="Second Field",
            cord=[{"lat": 24.0, "lng": 59.0}],
            colr="#00FF00",
            covr=Decimal("2.500"),
            loca="Second Location",
            work_shifts={"tuesday": [{"start": "09:00", "end": "17:00"}]}
        )

        Field.objects.create(
            name="Third Field",
            cord=[{"lat": 25.0, "lng": 60.0}],
            colr="#0000FF",
            covr=Decimal("3.750"),
            loca="Third Location",
            work_shifts={}
        )

        # Verify all fields exist
        all_fields = Field.objects.all()
        self.assertEqual(all_fields.count(), 3)

        # Verify each field has unique properties
        field_names = [f.name for f in all_fields]
        self.assertIn("Test Field", field_names)
        self.assertIn("Second Field", field_names)
        self.assertIn("Third Field", field_names)