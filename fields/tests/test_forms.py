from django.test import TestCase, tag
from fields.forms import FieldForm
from decimal import Decimal
from unittest.mock import patch


class BaseFieldFormTest(TestCase):
    """Base class for field form tests with common setup"""
    def setUp(self):
        self.valid_form_data = {
            'name': 'Test Field',
            'cord': '[{"lat": 23.5674, "lng": 58.3248}]',
            'colr': '#FF5733',
            'covr': Decimal('1.234'),
            'loca': 'Test Location',
            'work_shifts': '{"monday": [{"start": "08:00", "end": "16:00"}]}'
        }


@tag('unmock')
class FieldFormUnmockTest(BaseFieldFormTest):
    """Tests for field forms without using mocks"""

    def test_valid_form(self):
        """Test that the form is valid with correct data"""
        form = FieldForm(data=self.valid_form_data)
        self.assertTrue(form.is_valid())

    def test_blank_name(self):
        """Test that the form is invalid when name is blank"""
        invalid_data = self.valid_form_data.copy()
        invalid_data['name'] = ''
        form = FieldForm(data=invalid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)

    def test_form_widgets(self):
        """Test that the form has the expected widgets"""
        form = FieldForm()
        self.assertEqual(form.fields['name'].widget.attrs['class'], 'form-control col-sm-2')
        # Check that the color widget has the correct class
        self.assertEqual(form.fields['colr'].widget.attrs['class'], 'form-control col-sm-2')
        # Check that the cord widget has the correct class
        self.assertEqual(form.fields['cord'].widget.attrs['class'], 'form-control col-sm-2 d-none')

    def test_form_labels(self):
        """Test that the form has the expected labels"""
        form = FieldForm()
        self.assertEqual(form.fields['name'].label, 'Name')
        self.assertEqual(form.fields['colr'].label, 'Highlight Color')


@tag('mock')
class FieldFormMockTest(BaseFieldFormTest):
    """Tests for field forms using mocks"""

    @patch('fields.forms.FieldForm.clean')
    def test_form_clean_method_called(self, mock_clean):
        """Test that the clean method is called during form validation"""
        # Configure the mock to return a valid cleaned data
        mock_clean.return_value = self.valid_form_data

        # Create and validate the form
        form = FieldForm(data=self.valid_form_data)
        form.is_valid()

        # Verify the clean method was called
        mock_clean.assert_called_once()

    @patch('fields.utils.get_work_shifts_from_form')
    def test_form_with_mocked_work_shifts_util(self, mock_get_work_shifts):
        """Test form validation with a mocked work shifts utility function"""
        # Configure the mock to return a specific value
        mock_get_work_shifts.return_value = {"monday": ["08:00-16:00"]}

        # Create and validate the form
        form = FieldForm(data=self.valid_form_data)
        form.is_valid()

        # Note: We're not asserting the mock was called because the form doesn't directly call this function
        # This is just to demonstrate mocking a utility function that might be used in the form processing